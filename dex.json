{"network": "localhost", "contract": {"address": "0x5FC8d32690cc91D4c39d9d3abcBD16989F875707", "signerAddress": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "abi": "[{\"type\":\"constructor\",\"stateMutability\":\"undefined\",\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_token\"},{\"type\":\"uint256\",\"name\":\"_price\"}]},{\"type\":\"function\",\"name\":\"associatedToken\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"address\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"buy\",\"constant\":false,\"stateMutability\":\"payable\",\"payable\":true,\"inputs\":[{\"type\":\"uint256\",\"name\":\"numTokens\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"getPrice\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[{\"type\":\"uint256\",\"name\":\"numTokens\"}],\"outputs\":[{\"type\":\"uint256\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"getTokenBalance\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"uint256\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"sell\",\"constant\":false,\"payable\":false,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"withdrawFunds\",\"constant\":false,\"payable\":false,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"withdrawTokens\",\"constant\":false,\"payable\":false,\"inputs\":[],\"outputs\":[]}]"}}