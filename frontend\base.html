<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body></body>
</html>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DEX ERC-20</title>

    <style>
      .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
      }

      .row {
        display: flex;
        flex-direction: row;
        width: 100%;
      }

      .item {
        display: flex;
        flex-direction: column;
        flex: 1;
        align-items: center;
        justify-content: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>General Information</h2>
      <div class="row">
        <div class="item">
          <h3>Token Price: <span id="tokenPrice"></span></h3>
          <button onclick="getPrice()">Update Price</button>
        </div>
        <div class="item">
          <h3>Tokens Available: <span id="tokensAvailable"></span></h3>
          <button onclick="getAvailableTokens()">
            Update Tokens Available
          </button>
        </div>
        <div class="item">
          <h3>My Token Balance: <span id="tokensBalance"></span></h3>
          <button onclick="getTokenBalance()">Update My Tokens</button>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="row">
        <div class="item">
          <h3>Grant DEX Access</h3>
          <div>
            <input
              type="number"
              min="0"
              placeholder="Enter number of tokens"
              id="tokenGrant"
            />
            <button onclick="grantAccess()">Grant</button>
          </div>
        </div>
        <div class="item">
          <h3>Sell Tokens</h3>
          <button onclick="sell()">Sell</button>
        </div>
      </div>
    </div>
    <div class="container">
      <h2>User Controls</h2>
      <div class="item">
        <h3>Buy Tokens</h3>
        <div>
          <input
            type="number"
            min="0"
            placeholder="Enter number of tokens"
            id="tokensToBuy"
          />
          <button onclick="buy()">Buy</button>
        </div>
      </div>
    </div>
    <script
      src="https://cdn.jsdelivr.net/npm/ethers@5.7.2/dist/ethers.umd.min.js"
      type="application/javascript"
    ></script>
    <script type="text/javascript" src="script.js"></script>
  </body>
</html>
