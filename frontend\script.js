const provider = new ethers.providers.Web3Provider(window.ethereum);
let signer;

const tokenAbi = [
  {
    type: "constructor",
    stateMutability: "undefined",
    payable: false,
    inputs: [{ type: "uint256", name: "initialSupply" }],
  },
  {
    type: "error",
    name: "ERC20InsufficientAllowance",
    inputs: [
      { type: "address", name: "spender" },
      { type: "uint256", name: "allowance" },
      { type: "uint256", name: "needed" },
    ],
  },
  {
    type: "error",
    name: "ERC20InsufficientBalance",
    inputs: [
      { type: "address", name: "sender" },
      { type: "uint256", name: "balance" },
      { type: "uint256", name: "needed" },
    ],
  },
  {
    type: "error",
    name: "ERC20InvalidApprover",
    inputs: [{ type: "address", name: "approver" }],
  },
  {
    type: "error",
    name: "ERC20InvalidReceiver",
    inputs: [{ type: "address", name: "receiver" }],
  },
  {
    type: "error",
    name: "ERC20InvalidSender",
    inputs: [{ type: "address", name: "sender" }],
  },
  {
    type: "error",
    name: "ERC20InvalidSpender",
    inputs: [{ type: "address", name: "spender" }],
  },
  {
    type: "event",
    anonymous: false,
    name: "Approval",
    inputs: [
      { type: "address", name: "owner", indexed: true },
      { type: "address", name: "spender", indexed: true },
      { type: "uint256", name: "value", indexed: false },
    ],
  },
  {
    type: "event",
    anonymous: false,
    name: "Transfer",
    inputs: [
      { type: "address", name: "from", indexed: true },
      { type: "address", name: "to", indexed: true },
      { type: "uint256", name: "value", indexed: false },
    ],
  },
  {
    type: "function",
    name: "allowance",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [
      { type: "address", name: "owner" },
      { type: "address", name: "spender" },
    ],
    outputs: [{ type: "uint256", name: "" }],
  },
  {
    type: "function",
    name: "approve",
    constant: false,
    payable: false,
    inputs: [
      { type: "address", name: "spender" },
      { type: "uint256", name: "value" },
    ],
    outputs: [{ type: "bool", name: "" }],
  },
  {
    type: "function",
    name: "balanceOf",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [{ type: "address", name: "account" }],
    outputs: [{ type: "uint256", name: "" }],
  },
  {
    type: "function",
    name: "decimals",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "uint8", name: "" }],
  },
  {
    type: "function",
    name: "name",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "string", name: "" }],
  },
  {
    type: "function",
    name: "symbol",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "string", name: "" }],
  },
  {
    type: "function",
    name: "totalSupply",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "uint256", name: "" }],
  },
  {
    type: "function",
    name: "transfer",
    constant: false,
    payable: false,
    inputs: [
      { type: "address", name: "to" },
      { type: "uint256", name: "value" },
    ],
    outputs: [{ type: "bool", name: "" }],
  },
  {
    type: "function",
    name: "transferFrom",
    constant: false,
    payable: false,
    inputs: [
      { type: "address", name: "from" },
      { type: "address", name: "to" },
      { type: "uint256", name: "value" },
    ],
    outputs: [{ type: "bool", name: "" }],
  },
];
const tokenAddress = "0xDc64a140Aa3E981100a9becA4E685f962f0cF6C9";
let tokenContract = null;

const dexAbi = [
  {
    type: "constructor",
    stateMutability: "undefined",
    payable: false,
    inputs: [
      { type: "address", name: "_token" },
      { type: "uint256", name: "_price" },
    ],
  },
  {
    type: "function",
    name: "associatedToken",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "address", name: "" }],
  },
  {
    type: "function",
    name: "buy",
    constant: false,
    stateMutability: "payable",
    payable: true,
    inputs: [{ type: "uint256", name: "numTokens" }],
    outputs: [],
  },
  {
    type: "function",
    name: "getPrice",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [{ type: "uint256", name: "numTokens" }],
    outputs: [{ type: "uint256", name: "" }],
  },
  {
    type: "function",
    name: "getTokenBalance",
    constant: true,
    stateMutability: "view",
    payable: false,
    inputs: [],
    outputs: [{ type: "uint256", name: "" }],
  },
  {
    type: "function",
    name: "sell",
    constant: false,
    payable: false,
    inputs: [],
    outputs: [],
  },
  {
    type: "function",
    name: "withdrawFunds",
    constant: false,
    payable: false,
    inputs: [],
    outputs: [],
  },
  {
    type: "function",
    name: "withdrawTokens",
    constant: false,
    payable: false,
    inputs: [],
    outputs: [],
  },
];
const dexAddress = "******************************************";
let dexContract = null;

async function getAccess() {
  if (tokenContract) return;
  await provider.send("eth_requestAccounts", []);
  signer = provider.getSigner();
  tokenContract = new ethers.Contract(tokenAddress, tokenAbi, signer);
  dexContract = new ethers.Contract(dexAddress, dexAbi, signer);
}

async function getPrice() {
  await getAccess();
  const price = await dexContract.getPrice(1);
  document.getElementById("tokenPrice").innerHTML = price;
  return price;
}

async function getTokenBalance() {
  await getAccess();
  const balance = await tokenContract.balanceOf(await signer.getAddress());
  document.getElementById("tokenBalance").innerHTML = balance;
}

async function getAvailableTokens() {
  await getAccess();
  const tokens = await dexContract.getTokenBalance();
  document.getElementById("tokensAvailable").innerHTML = tokens;
  return tokens;
}
